#ifndef __DIC_H
#define __DIC_H

#include "delay.h"
//#include "usb_device.h"
//#include "usbd_cdc.h" 
//#include "usbd_cdc_if.h" 

#define SDO_W  	 0x20
#define SDO_W_4  0x23 
#define SDO_W_2  0x2b 
#define SDO_W_1  0x2f 
#define SDO_R  	 0x40
#define SDO_R_4  0x43
#define SDO_R_2  0x4b 
#define SDO_R_1  0x4f 
typedef struct 
{
	u16       Index;      //索引
	u8        SubIndex;   //字索引
	u8        Data_type;        //对象长度，用于表示数据类型（单位：字节）
	u8*       OD_pointer;   //访问类型：RO、RW、WO、CONST  
	u32       Max;     //对象允许的最小值 
	u32       Min;    //对象允许的最大值 
	u8        Flags;	    //对象对应的变量地址
	u8        Flags1;	    //对象对应的变量地址
	u8        Flags2;	    //对象对应的变量地址
	u8        Flags3;	    //对象对应的变量地址
	void      (* Fp_R)( void );       //对于复杂对象的操作，使用该函数
	void      (* Fp_W)( void );       //对于复杂对象的操作，使用该函数
}DICT_OBJECT;


void fdummy( void ); 
u8 Save_Parameter(void);  
u8 Read_Parameter(void);
u8 Index_To_RSDO(long long index,u8 *pbuf);
u8 Index_To_WSDO(long long index,u8 *pbuf,u8 *data);
u8 RS232_Read_Index(u8 id,long long index,u8 *pbuf);
u8 RS232_Write_Index(u8 id,long long index,u8 *pbuf,u8 *data);
u16 Write_OD( DICT_OBJECT Access_OBJ );
u16 Read_OD( DICT_OBJECT Access_OBJ );
void RS485_SDO_Service(void);
void VCP_Service_Process(void);

extern DICT_OBJECT Access_OBJ;
extern u8 Eerom_Buffer[50][8];
extern u8 Write_Access;
//extern USBD_HandleTypeDef hUsbDeviceFS;
extern u8 VCP_RX_Buffer[100];
extern u8 VCP_TX_Buffer[100];
extern u16 VCP_RX_Len;



#endif
