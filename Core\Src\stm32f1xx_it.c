/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    stm32f1xx_it.c
  * @brief   Interrupt Service Routines.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2020 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "stm32f1xx_it.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "mcpwm.h"
#include "mt6825.h"
#include "niming.h"
#include "vofa_function.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN TD */

/* USER CODE END TD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */
int t1,t2,t3,t4,t5,t6;             // 临时变量，用于时间测量或调试
int tt1,tt2,tt3,tt4,tt5,tt6;       // 临时变量，用于时间测量或调试
int ttt1,ttt2,ttt3=0,ttt4=0;       // 临时变量，用于时间测量或调试
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/* External variables --------------------------------------------------------*/
extern ADC_HandleTypeDef hadc1;      // ADC1句柄
extern ADC_HandleTypeDef hadc2;      // ADC2句柄
extern CAN_HandleTypeDef hcan;       // CAN通信句柄
extern TIM_HandleTypeDef htim1;      // 定时器1句柄（可能用于PWM生成）
extern TIM_HandleTypeDef htim4;      // 定时器4句柄（可能用于周期性任务）
extern DMA_HandleTypeDef hdma_usart1_rx;  // USART1接收DMA句柄
extern DMA_HandleTypeDef hdma_usart2_rx;  // USART2接收DMA句柄
extern DMA_HandleTypeDef hdma_usart2_tx;  // USART2发送DMA句柄
extern DMA_HandleTypeDef hdma_usart3_rx;  // USART3接收DMA句柄
extern DMA_HandleTypeDef hdma_usart3_tx;  // USART3发送DMA句柄
extern UART_HandleTypeDef huart1;     // UART1句柄
extern UART_HandleTypeDef huart2;     // UART2句柄
extern UART_HandleTypeDef huart3;     // UART3句柄
/* USER CODE BEGIN EV */

/* USER CODE END EV */

/******************************************************************************/
/*           Cortex-M3 Processor Interruption and Exception Handlers          */
/******************************************************************************/
/**
  * @brief This function handles Non maskable interrupt.
  */
void NMI_Handler(void)
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */
  // 不可屏蔽中断处理函数
  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */

  /* USER CODE END NonMaskableInt_IRQn 1 */
}

/**
  * @brief This function handles Hard fault interrupt.
  */
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */
  // 硬件错误中断处理函数
  /* USER CODE END HardFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_HardFault_IRQn 0 */
    // 硬件错误时进入无限循环
    /* USER CODE END W1_HardFault_IRQn 0 */
  }
}

/**
  * @brief This function handles Memory management fault.
  */
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_MemoryManagement_IRQn 0 */
    /* USER CODE END W1_MemoryManagement_IRQn 0 */
  }
}

/**
  * @brief This function handles Prefetch fault, memory access fault.
  */
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_BusFault_IRQn 0 */
    /* USER CODE END W1_BusFault_IRQn 0 */
  }
}

/**
  * @brief This function handles Undefined instruction or illegal state.
  */
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_UsageFault_IRQn 0 */
    /* USER CODE END W1_UsageFault_IRQn 0 */
  }
}

/**
  * @brief This function handles System service call via SWI instruction.
  */
void SVC_Handler(void)
{
  /* USER CODE BEGIN SVCall_IRQn 0 */

  /* USER CODE END SVCall_IRQn 0 */
  /* USER CODE BEGIN SVCall_IRQn 1 */

  /* USER CODE END SVCall_IRQn 1 */
}

/**
  * @brief This function handles Debug monitor.
  */
void DebugMon_Handler(void)
{
  /* USER CODE BEGIN DebugMonitor_IRQn 0 */

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}

/**
  * @brief This function handles Pendable request for system service.
  */
void PendSV_Handler(void)
{
  /* USER CODE BEGIN PendSV_IRQn 0 */

  /* USER CODE END PendSV_IRQn 0 */
  /* USER CODE BEGIN PendSV_IRQn 1 */

  /* USER CODE END PendSV_IRQn 1 */
}

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
  /* USER CODE BEGIN SysTick_IRQn 0 */
	LED_Process();  // 系统滴答定时器中处理LED状态
  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();    // 增加HAL库的系统时钟计数
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}

/******************************************************************************/
/* STM32F1xx Peripheral Interrupt Handlers                                    */
/* Add here the Interrupt Handlers for the used peripherals.                  */
/* For the available peripheral interrupt handler names,                      */
/* please refer to the startup file (startup_stm32f1xx.s).                    */
/******************************************************************************/

/**
  * @brief This function handles RCC global interrupt.
  */
void RCC_IRQHandler(void)
{
  /* USER CODE BEGIN RCC_IRQn 0 */

  /* USER CODE END RCC_IRQn 0 */
  /* USER CODE BEGIN RCC_IRQn 1 */

  /* USER CODE END RCC_IRQn 1 */
}

/**
  * @brief This function handles DMA1 channel2 global interrupt.
  */
void DMA1_Channel2_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel2_IRQn 0 */

  /* USER CODE END DMA1_Channel2_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart3_tx);
  /* USER CODE BEGIN DMA1_Channel2_IRQn 1 */

  /* USER CODE END DMA1_Channel2_IRQn 1 */
}

/**
  * @brief This function handles DMA1 channel3 global interrupt.
  */
void DMA1_Channel3_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel3_IRQn 0 */

  /* USER CODE END DMA1_Channel3_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart3_rx);
  /* USER CODE BEGIN DMA1_Channel3_IRQn 1 */

  /* USER CODE END DMA1_Channel3_IRQn 1 */
}

/**
  * @brief This function handles DMA1 channel5 global interrupt.
  */
void DMA1_Channel5_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel5_IRQn 0 */

  /* USER CODE END DMA1_Channel5_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart1_rx);
  /* USER CODE BEGIN DMA1_Channel5_IRQn 1 */

  /* USER CODE END DMA1_Channel5_IRQn 1 */
}

/**
  * @brief This function handles DMA1 channel6 global interrupt.
  */
void DMA1_Channel6_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel6_IRQn 0 */

  /* USER CODE END DMA1_Channel6_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart2_rx);
  /* USER CODE BEGIN DMA1_Channel6_IRQn 1 */

  /* USER CODE END DMA1_Channel6_IRQn 1 */
}

/**
  * @brief This function handles DMA1 channel7 global interrupt.
  */
void DMA1_Channel7_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel7_IRQn 0 */

  /* USER CODE END DMA1_Channel7_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart2_tx);
  /* USER CODE BEGIN DMA1_Channel7_IRQn 1 */

  /* USER CODE END DMA1_Channel7_IRQn 1 */
}

/**
  * @brief This function handles ADC1 and ADC2 global interrupts.
  */
void ADC1_2_IRQHandler(void)
{
  /* USER CODE BEGIN ADC1_2_IRQn 0 */
  // ADC1和ADC2全局中断处理
  /* USER CODE END ADC1_2_IRQn 0 */
  HAL_ADC_IRQHandler(&hadc1);  // 调用ADC1中断处理函数
  HAL_ADC_IRQHandler(&hadc2);  // 调用ADC2中断处理函数
  /* USER CODE BEGIN ADC1_2_IRQn 1 */

  /* USER CODE END ADC1_2_IRQn 1 */
}

/**
  * @brief This function handles USB low priority or CAN RX0 interrupts.
  */
void USB_LP_CAN1_RX0_IRQHandler(void)
{
  /* USER CODE BEGIN USB_LP_CAN1_RX0_IRQn 0 */
  // USB低优先级或CAN接收中断处理
  /* USER CODE END USB_LP_CAN1_RX0_IRQn 0 */
  HAL_CAN_IRQHandler(&hcan);  // 调用CAN中断处理函数
  /* USER CODE BEGIN USB_LP_CAN1_RX0_IRQn 1 */

  /* USER CODE END USB_LP_CAN1_RX0_IRQn 1 */
}

/**
  * @brief This function handles TIM1 capture compare interrupt.
  */
void TIM1_CC_IRQHandler(void)
{
  /* USER CODE BEGIN TIM1_CC_IRQn 0 */
	ADCValue[0]=HAL_ADCEx_InjectedGetValue(&hadc1, 1);  // 获取ADC1注入通道1的值
	ADCValue[1]=HAL_ADCEx_InjectedGetValue(&hadc2, 1);  // 获取ADC2注入通道1的值
	ADCValue[2]=HAL_ADC_GetValue(&hadc1);               // 获取ADC1常规转换的值
	ADCValue[3]=HAL_ADC_GetValue(&hadc2);               // 获取ADC2常规转换的值
	
	vbus_voltage=(ADCValue[3]*19)/100;                  // 计算总线电压
  
	NTC_R_Value=1000*ADCValue[2]/(4096-ADCValue[2]);    // 计算NTC电阻值
	device_temperature=Get_NTC_Temperature(NTC_R_Value); // 根据NTC电阻获取温度
	
	if(Driver_Ready)  // 如果驱动器已准备好
	{
		loop_counter_c++;  // 电流环计数器递增
		loop_counter_v++;  // 速度环计数器递增
		loop_counter_p++;  // 位置环计数器递增
		
		update_motor(&motor);  // 更新电机状态
		{
			current_loop_ready=1;  // 标记电流环准备就绪
			Current_loop(&motor, Id_demand, Iq_demand);  // 执行电流环控制
			loop_counter_c=0;  // 重置电流环计数器
		}
		if(loop_counter_v>3)  // 速度环执行频率是电流环的1/4
		{
			switch(feedback_type)  // 根据不同反馈类型执行不同操作
			{
				case 4:
					// 从Tamagawa编码器读取数据，大约需要25us
					if(set_tamagawa_zero==0)  // 正常读取模式
					{
		
						Tamagawa_TX_BUFF[0]=0x02;  // 设置Tamagawa命令
						Tamagawa_Read_Cmd(Tamagawa_TX_BUFF,1);  // 发送读取命令
						
						if(Tamagawa_First==10)  // 如果编码器已初始化
						{
							Tamagawa_count_temp++;  // 计数器增加
							if(Tamagawa_count_temp>1)
							{
								Tamagawa_lost++;  // 记录丢失次数
							}
							if(Tamagawa_count_temp>20)  // 如果连续20次无响应
							{
								if(set_tamagawa_zero==0)
									Error_State.bits.ENC_error=1;  // 设置编码器错误标志
							}
						}
					}
					else if(set_tamagawa_zero==1)  // 零位设置模式
					{
		
						Tamagawa_TX_BUFF[0]=0xAA;  // 设置零位命令
						Tamagawa_Read_Cmd(Tamagawa_TX_BUFF,1);
						set_tamagawa_zero=4;  // 更新状态
					}
					else if(set_tamagawa_zero==5)  // 零位确认模式
					{
						set_tamagawa_zero_count++;
						if(set_tamagawa_zero_count<12)  // 尝试12次
						{
							Tamagawa_TX_BUFF[0]=0xC2;  // 确认命令
							Tamagawa_Read_Cmd(Tamagawa_TX_BUFF,1);
						}
						else
						{
							set_tamagawa_zero=0;  // 恢复正常模式
							set_tamagawa_zero_count=0;  // 清零计数器
						}
					}
				
					break;
				case 5:
					// Tamagawa编码器的另一种操作模式
					// ...existing code...
					break;
				case 8:
					//spi读取编码器数据
					tamagawa_angle = (uint16_t)ReadValue(0xffff);  // 通过SPI读取角度值
          // vofaSendUint16AsFloat(&tamagawa_angle,1);
				break;
				default:
					break;
			}
		
			//HAL_GPIO_TogglePin(ERR_GPIO_Port, ERR_Pin);
			velocity_loop_ready=1;  // 标记速度环准备就绪
			Velocity_loop(&motor,speed_demand);  // 执行速度环控制
			loop_counter_v=0;  // 重置速度环计数器
		}

		if(loop_counter_p>15)  // 位置环执行频率是电流环的1/16
		{
			position_loop_ready=1;  // 标记位置环准备就绪
			Position_Loop(&motor,position_demand);  // 执行位置环控制
      // ANO_DT_Send_USER_DATA(0xF3,3);
			loop_counter_p=0;  // 重置位置环计数器
		}
	}
	
  /* USER CODE END TIM1_CC_IRQn 0 */
  HAL_TIM_IRQHandler(&htim1);  // 调用TIM1中断处理函数
  /* USER CODE BEGIN TIM1_CC_IRQn 1 */

  /* USER CODE END TIM1_CC_IRQn 1 */
}

/**
  * @brief This function handles TIM4 global interrupt.
  */
void TIM4_IRQHandler(void)
{
  /* USER CODE BEGIN TIM4_IRQn 0 */
	tim_count++;  // 定时器计数增加
  // float mixed_data[] = {
  //   // (float)kcp,                // float类型
  //   // (float)kci,                // float类型
  //   // (float)target_speed,
  //   // (float)real_speed,
  //   currentU_show,
  //   currentV_show,
  //   currentW_show,
  //   // (float)Id_demand,
  //   // (float)Id,
  //   // (float)Id_real,
  //   };
  // vofaSendMixedDataAsFloat(mixed_data, 2);
	if(tim_count>=TPDO_Period)  // 达到TPDO周期
	{
		tim_count=0;  // 重置计数器
		Process_TPDO();  // 处理TPDO（传输过程数据对象，CANopen协议）

	}
		if(position_loop_ready==1)  // 如果位置环准备就绪
		{
			if(motor_on)
			Motion_process();  // 执行运动处理
			position_loop_ready=0;  // 清除位置环就绪标志
			
		}
  /* USER CODE END TIM4_IRQn 0 */
  HAL_TIM_IRQHandler(&htim4);  // 调用TIM4中断处理函数
  /* USER CODE BEGIN TIM4_IRQn 1 */

  /* USER CODE END TIM4_IRQn 1 */
}

/**
  * @brief This function handles USART1 global interrupt.
  */
void USART1_IRQHandler(void)
{
  /* USER CODE BEGIN USART1_IRQn 0 */
  // USART1全局中断处理
  /* USER CODE END USART1_IRQn 0 */
  HAL_UART_IRQHandler(&huart1);  // 调用UART1中断处理函数
  /* USER CODE BEGIN USART1_IRQn 1 */

  /* USER CODE END USART1_IRQn 1 */
}

/**
  * @brief This function handles USART2 global interrupt.
  */
void USART2_IRQHandler(void)
{
  /* USER CODE BEGIN USART2_IRQn 0 */
  // USART2全局中断处理
  /* USER CODE END USART2_IRQn 0 */
  HAL_UART_IRQHandler(&huart2);  // 调用UART2中断处理函数
  /* USER CODE BEGIN USART2_IRQn 1 */

  // 判断是不是串口2
  if(USART2 == huart2.Instance)                                   
  {
    // 判断是不是空闲中断
    if(RESET != __HAL_UART_GET_FLAG(&huart2, UART_FLAG_IDLE))   
    {	
      // 清除空闲中断标志（不然会一直不断进入中断）
      __HAL_UART_CLEAR_IDLEFLAG(&huart2); 
      HAL_UART_DMAStop(&huart2); 
      RS232_RX_CNT = USART2_REC_LEN - __HAL_DMA_GET_COUNTER(&hdma_usart2_rx);   
      if(RS232_RX_CNT > 0)
      {
        // 设置帧接收标志
        RS232_FrameFlag = 1;
        // 处理接收到的数据
        for(uint16_t i = 0; i < RS232_RX_CNT; i++)
        {
          uartCMDRecv(RS232_RX_BUFF[i]);
          if(vofaCommandData.completionFlag)
          { 
            vofaCommandParse();
            vofaCommandData.completionFlag = 0;
          }
        }
        // 处理其他协议数据
        // RS232_Solve_Service();
      }
      
      // 清零接收缓冲区
      memset(RS232_RX_BUFF, 0, RS232_RX_CNT); 
      RS232_RX_CNT = 0;
      
      // 重新启动DMA接收
      HAL_UART_Receive_DMA(&huart2, (uint8_t*)RS232_RX_BUFF, USART2_REC_LEN);
    }
  }
  /* USER CODE END USART2_IRQn 1 */
}

/**
  * @brief This function handles USART3 global interrupt.
  */
void USART3_IRQHandler(void)
{
  /* USER CODE BEGIN USART3_IRQn 0 */

  /* USER CODE END USART3_IRQn 0 */
  HAL_UART_IRQHandler(&huart3);
  /* USER CODE BEGIN USART3_IRQn 1 */

    if(USART3 == huart3.Instance)                                   
    {	// 判断是不是空闲中断
        if(RESET != __HAL_UART_GET_FLAG(&huart3, UART_FLAG_IDLE))   
        {	 // 清除空闲中断标志（不然会一直不断进入中断）
	          tt1=SysTick->VAL;

            __HAL_UART_CLEAR_IDLEFLAG(&huart3);                    
            //printf("\r\nUART1 Idle IQR Detected\r\n");
           // 中止本次DMA传输
						HAL_UART_DMAStop(&huart3); 
						//__HAL_UNLOCK(&hdma_usart1_rx);					
						Modbus_Solve_485_Enable();																									 
						// 计算接收到的数据长度
						RS485_RX_CNT  = USART3_REC_LEN - __HAL_DMA_GET_COUNTER(&hdma_usart3_rx);   
						RS485_FrameFlag = 1;
						Modbus_Solve_Service();      
					// 测试函数：将接收到的数据打印出去
						// printf("Receive Data(length = %d): ",data_length);
						// HAL_UART_Transmit(&huart1,RS485_RX_BUFF,data_length,0x200);                     
						// printf("\r\n");
						
					// 清零接收缓冲区
						memset(RS485_RX_BUFF,0,RS485_RX_CNT); 
							
						RS485_RX_CNT = 0;
						
						// 重新启动DMA接收，这是解决问题的关键
						HAL_UART_Receive_DMA(&huart3, (uint8_t*)RS485_RX_BUFF, USART3_REC_LEN);
					}
				}
//						Modbus_Solve_485_Disenable();	
//						// 重启开始DMA传输 每次255字节数据
//						HAL_UART_Receive_DMA(&huart1, (uint8_t*)RS485_RX_BUFF, USART1_REC_LEN);   
  /* USER CODE END USART3_IRQn 1 */
}

/* USER CODE BEGIN 1 */

/* USER CODE END 1 */
